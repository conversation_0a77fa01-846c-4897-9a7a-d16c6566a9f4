#!/usr/bin/env python3
"""
Test script for brainstorming improvements.

This script tests the domain adaptation and dynamic temperature features
for the brainstorming agents.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vibe_coder.core.consultation.domain_adaptation import DomainAdaptivePromptManager
from vibe_coder.core.consultation.temperature_manager import DynamicTemperatureManager


def test_domain_detection():
    """Test domain detection functionality."""
    print("🔍 Testing Domain Detection...")
    
    manager = DomainAdaptivePromptManager()
    
    test_cases = [
        "I want to build a calculator app",
        "I need a health monitoring system for brain wellness",
        "Create a truck fleet maintenance tracker",
        "Build a personal finance budgeting tool",
        "Design a learning management system for students"
    ]
    
    for description in test_cases:
        domain, confidence = manager.domain_detector.detect_domain(description)
        print(f"  '{description}' → {domain} (confidence: {confidence:.2f})")
    
    print("✅ Domain detection test completed\n")


def test_prompt_adaptation():
    """Test prompt adaptation functionality."""
    print("🎯 Testing Prompt Adaptation...")
    
    manager = DomainAdaptivePromptManager()
    
    # Test calculator domain
    calculator_description = "I want to build a scientific calculator app"
    adapted_prompts = manager.adapt_agent_prompts(calculator_description)
    
    print(f"Calculator App Domain Adaptation:")
    print(f"  Muse prompt preview: {adapted_prompts['muse'][:100]}...")
    print(f"  Oracle prompt preview: {adapted_prompts['oracle'][:100]}...")
    print(f"  Wisdom prompt preview: {adapted_prompts['wisdom'][:100]}...")
    print(f"  Scout prompt preview: {adapted_prompts['scout'][:100]}...")
    
    print("✅ Prompt adaptation test completed\n")


def test_temperature_management():
    """Test dynamic temperature management."""
    print("🌡️ Testing Dynamic Temperature Management...")
    
    manager = DynamicTemperatureManager()
    
    # Simulate conversation progression
    test_scenarios = [
        {
            "name": "Early brainstorming (divergent)",
            "messages": [
                {"sender": "User", "message": "I want to build something cool"},
                {"sender": "Muse", "message": "What about a quantum calculator?"},
                {"sender": "Oracle", "message": "That seems very ambitious"}
            ],
            "shared_context": {"convergence_state": "divergent"}
        },
        {
            "name": "Working convergence",
            "messages": [
                {"sender": "User", "message": "I agree, let's focus on a simple calculator"},
                {"sender": "Muse", "message": "Yes, building on that idea..."},
                {"sender": "Oracle", "message": "I agree, that's more realistic"},
                {"sender": "Wisdom", "message": "Good consensus emerging"}
            ],
            "shared_context": {"convergence_state": "working"}
        },
        {
            "name": "Strong convergence",
            "messages": [
                {"sender": "User", "message": "Perfect, let's implement the basic calculator"},
                {"sender": "Muse", "message": "Exactly, with clean UI design"},
                {"sender": "Oracle", "message": "Agreed, focusing on core functionality"},
                {"sender": "Wisdom", "message": "This approach aligns well with our goals"}
            ],
            "shared_context": {"convergence_state": "strong"}
        }
    ]
    
    for scenario in test_scenarios:
        print(f"  Scenario: {scenario['name']}")
        temperatures = manager.get_all_agent_temperatures(
            scenario["messages"], 
            scenario["shared_context"]
        )
        
        for agent, temp in temperatures.items():
            print(f"    {agent}: {temp:.2f}")
        print()
    
    print("✅ Temperature management test completed\n")


def test_config_assignments():
    """Test LLM assignment configuration."""
    print("🤖 Testing LLM Assignment Configuration...")
    
    try:
        from vibe_coder.config import get_config
        config = get_config()
        
        agent_assignments = config.get("agent_assignments", {})
        
        expected_assignments = {
            "muse_agent": "openrouter",
            "oracle_agent": "deepseek", 
            "wisdom_layer": "openrouter",
            "open_source_scout": "openrouter"
        }
        
        print("  Current agent assignments:")
        for agent, provider in expected_assignments.items():
            actual = agent_assignments.get(agent, "NOT SET")
            status = "✅" if actual == provider else "❌"
            print(f"    {agent}: {actual} {status}")
        
        print("✅ LLM assignment test completed\n")
        
    except Exception as e:
        print(f"❌ Config test failed: {e}\n")


def main():
    """Run all tests."""
    print("🚀 Testing Brainstorming Improvements\n")
    print("=" * 50)
    
    try:
        test_domain_detection()
        test_prompt_adaptation()
        test_temperature_management()
        test_config_assignments()
        
        print("🎉 All tests completed successfully!")
        print("\n📋 Summary of Improvements:")
        print("  ✅ Domain-adaptive prompts for focused agent responses")
        print("  ✅ Dynamic temperature adjustment based on convergence")
        print("  ✅ LLM provider assignments for agent specialization")
        print("  ✅ Enhanced context awareness for better brainstorming")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
