"""
Domain-adaptive prompt system for brainstorming agents.

This module provides functionality to detect project domains and adapt
agent prompts to be more subject-specific and focused.
"""

import logging
import re
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DomainContext:
    """Context information for a specific domain."""
    name: str
    keywords: List[str]
    muse_focus: str
    oracle_focus: str
    wisdom_focus: str
    scout_focus: str


class DomainDetector:
    """Detects project domain from user input and conversation context."""
    
    def __init__(self):
        """Initialize domain detector with predefined domains."""
        self.domains = {
            "calculator": DomainContext(
                name="Mathematical Software",
                keywords=["calculator", "math", "arithmetic", "scientific", "equation", "formula", "computation"],
                muse_focus="UI/UX innovation for mathematical interfaces, novel input methods, accessibility features, and mathematical visualization techniques",
                oracle_focus="floating-point precision risks, mathematical accuracy concerns, input validation challenges, and performance with complex calculations",
                wisdom_focus="mathematical correctness, accessibility compliance, user safety with financial calculations, and educational value",
                scout_focus="existing calculator frameworks, mathematical libraries, UI component libraries, and accessibility standards"
            ),
            "health": DomainContext(
                name="Digital Health & Wellness",
                keywords=["health", "medical", "brain", "fitness", "wellness", "monitoring", "symptoms", "diagnosis", "treatment"],
                muse_focus="innovative health monitoring methods, gamification of wellness, non-invasive tracking techniques, and user engagement strategies",
                oracle_focus="medical accuracy risks, privacy concerns with health data, regulatory compliance (FDA/HIPAA), false positive/negative rates, and liability issues",
                wisdom_focus="patient safety, medical ethics, data privacy, avoiding harmful medical advice, and ensuring appropriate medical disclaimers",
                scout_focus="health monitoring APIs, medical device integration, privacy-compliant health platforms, and regulatory guidelines"
            ),
            "automotive": DomainContext(
                name="Automotive & Fleet Management",
                keywords=["truck", "vehicle", "fleet", "maintenance", "automotive", "servicing", "repair", "parts", "logistics"],
                muse_focus="predictive maintenance innovations, IoT sensor integration, mobile mechanic workflows, and parts inventory optimization",
                oracle_focus="safety risks with maintenance recommendations, liability concerns, integration challenges with existing fleet systems, and data accuracy issues",
                wisdom_focus="safety compliance, cost-effectiveness for fleet operators, reliability of maintenance predictions, and industry standard adherence",
                scout_focus="fleet management platforms, IoT sensor technologies, maintenance scheduling systems, and industry compliance frameworks"
            ),
            "finance": DomainContext(
                name="Financial Technology",
                keywords=["money", "budget", "investment", "banking", "finance", "payment", "transaction", "accounting", "trading"],
                muse_focus="innovative financial interfaces, automated budgeting approaches, investment visualization, and user financial education features",
                oracle_focus="financial security risks, regulatory compliance (PCI DSS, SOX), data protection concerns, and accuracy of financial calculations",
                wisdom_focus="financial responsibility, user financial safety, regulatory compliance, and ethical financial guidance",
                scout_focus="financial APIs, security frameworks, compliance tools, and established financial platforms"
            ),
            "education": DomainContext(
                name="Educational Technology",
                keywords=["learning", "study", "student", "teaching", "education", "course", "training", "knowledge", "skill"],
                muse_focus="innovative learning methods, gamification of education, personalized learning paths, and engagement techniques",
                oracle_focus="learning effectiveness risks, age-appropriate content concerns, privacy issues with student data, and accessibility challenges",
                wisdom_focus="educational value, age-appropriate design, student privacy protection, and inclusive learning approaches",
                scout_focus="educational platforms, learning management systems, accessibility tools, and pedagogical frameworks"
            ),
            "productivity": DomainContext(
                name="Productivity & Task Management",
                keywords=["task", "productivity", "workflow", "organization", "planning", "schedule", "project", "management", "efficiency"],
                muse_focus="innovative task organization methods, workflow automation, user motivation techniques, and productivity visualization",
                oracle_focus="workflow complexity risks, user adoption challenges, data synchronization issues, and productivity measurement accuracy",
                wisdom_focus="work-life balance, sustainable productivity practices, user well-being, and effective task management principles",
                scout_focus="productivity frameworks, task management APIs, workflow automation tools, and collaboration platforms"
            ),
            "gaming": DomainContext(
                name="Gaming & Entertainment",
                keywords=["game", "gaming", "entertainment", "play", "player", "level", "score", "challenge", "fun"],
                muse_focus="innovative gameplay mechanics, player engagement strategies, creative game narratives, and immersive experiences",
                oracle_focus="player addiction concerns, age-appropriate content risks, monetization ethics, and technical performance challenges",
                wisdom_focus="responsible gaming practices, player well-being, inclusive design, and positive social impact",
                scout_focus="game engines, player analytics platforms, monetization frameworks, and gaming accessibility tools"
            )
        }
        
        logger.info(f"DomainDetector initialized with {len(self.domains)} domains")
    
    def detect_domain(self, text: str, conversation_history: List[Dict] = None) -> Tuple[str, float]:
        """
        Detect the most likely domain from text input.
        
        Args:
            text: Primary text to analyze (usually user's initial input)
            conversation_history: Optional conversation context for better detection
            
        Returns:
            Tuple of (domain_name, confidence_score)
        """
        # Combine text with conversation history for analysis
        full_text = text.lower()
        
        if conversation_history:
            # Add recent conversation messages
            for msg in conversation_history[-5:]:  # Last 5 messages
                if isinstance(msg, dict) and 'message' in msg:
                    full_text += " " + msg['message'].lower()
        
        # Score each domain
        domain_scores = {}
        
        for domain_name, domain_context in self.domains.items():
            score = self._calculate_domain_score(full_text, domain_context.keywords)
            domain_scores[domain_name] = score
        
        # Find best match
        best_domain = max(domain_scores, key=domain_scores.get)
        best_score = domain_scores[best_domain]
        
        # If no strong match, default to productivity
        if best_score < 0.1:
            best_domain = "productivity"
            best_score = 0.1
        
        logger.info(f"Detected domain: {best_domain} (confidence: {best_score:.2f})")
        return best_domain, best_score
    
    def _calculate_domain_score(self, text: str, keywords: List[str]) -> float:
        """Calculate domain match score based on keyword presence."""
        words = re.findall(r'\b\w+\b', text.lower())
        word_count = len(words)
        
        if word_count == 0:
            return 0.0
        
        # Count keyword matches
        matches = 0
        for keyword in keywords:
            # Count both exact matches and partial matches
            if keyword in text:
                matches += text.count(keyword)
        
        # Calculate score as ratio of matches to total words
        score = matches / word_count
        
        # Boost score if multiple different keywords match
        unique_matches = sum(1 for keyword in keywords if keyword in text)
        if unique_matches > 1:
            score *= (1 + (unique_matches - 1) * 0.2)  # 20% boost per additional keyword
        
        return min(score, 1.0)  # Cap at 1.0
    
    def get_domain_context(self, domain_name: str) -> DomainContext:
        """Get domain context for a specific domain."""
        return self.domains.get(domain_name, self.domains["productivity"])


class DomainAdaptivePromptManager:
    """Manages domain-adaptive prompts for brainstorming agents."""
    
    def __init__(self):
        """Initialize the prompt manager."""
        self.domain_detector = DomainDetector()
        self.base_prompts = {}
        logger.info("DomainAdaptivePromptManager initialized")
    
    def adapt_agent_prompts(self, project_description: str, conversation_history: List[Dict] = None) -> Dict[str, str]:
        """
        Generate domain-adapted prompts for all brainstorming agents.
        
        Args:
            project_description: Main project description from user
            conversation_history: Optional conversation context
            
        Returns:
            Dictionary mapping agent names to adapted prompts
        """
        # Detect domain
        domain_name, confidence = self.domain_detector.detect_domain(
            project_description, conversation_history
        )
        
        domain_context = self.domain_detector.get_domain_context(domain_name)
        
        # Generate adapted prompts
        adapted_prompts = {
            "muse": self._create_muse_prompt(domain_context),
            "oracle": self._create_oracle_prompt(domain_context),
            "wisdom": self._create_wisdom_prompt(domain_context),
            "scout": self._create_scout_prompt(domain_context)
        }
        
        logger.info(f"Generated domain-adapted prompts for {domain_context.name}")
        return adapted_prompts
    
    def _create_muse_prompt(self, domain: DomainContext) -> str:
        """Create domain-adapted Muse prompt."""
        return f"""You are the Muse, the creative ideation agent specializing in {domain.name} projects.

Your role is to generate innovative, creative, and breakthrough ideas while staying relevant to the domain.

DOMAIN EXPERTISE: {domain.muse_focus}

Core Principles:
- Generate creative but RELEVANT ideas for {domain.name}
- Push boundaries while maintaining practical applicability
- Focus on user experience and innovative approaches
- Build on team discussion with creative enhancements
- Reference other team members' contributions constructively

When brainstorming, consider:
- What innovative approaches could revolutionize this {domain.name} solution?
- How can we make this more engaging and user-friendly?
- What creative features would differentiate this from existing solutions?
- How can we solve user problems in unexpected but valuable ways?

Stay creative but focused on {domain.name} challenges and opportunities."""

    def _create_oracle_prompt(self, domain: DomainContext) -> str:
        """Create domain-adapted Oracle prompt."""
        return f"""You are the Oracle, the risk assessment agent specializing in {domain.name} projects.

Your role is to identify potential risks, challenges, and failure points specific to this domain.

DOMAIN EXPERTISE: {domain.oracle_focus}

Core Principles:
- Assess risks specific to {domain.name} development
- Provide evidence-based analysis and realistic concerns
- Identify technical, regulatory, and user adoption challenges
- Build on team discussion with constructive risk analysis
- Suggest practical mitigation strategies

When assessing risks, focus on:
- What could go wrong specifically in {domain.name} projects?
- What regulatory or compliance issues should we consider?
- What technical challenges are common in this domain?
- What user adoption barriers might we face?
- How can we mitigate these risks early in development?

Be realistic but constructive in your risk assessment."""

    def _create_wisdom_prompt(self, domain: DomainContext) -> str:
        """Create domain-adapted Wisdom prompt."""
        return f"""You are the Wisdom Layer, the strategic synthesis agent specializing in {domain.name} projects.

Your role is to synthesize team perspectives and provide strategic guidance for this domain.

DOMAIN EXPERTISE: {domain.wisdom_focus}

Core Principles:
- Balance creative ideas with practical constraints
- Synthesize Muse creativity with Oracle's risk assessment
- Provide strategic direction specific to {domain.name}
- Consider long-term implications and best practices
- Guide team toward convergence on strong solutions

When synthesizing, consider:
- How do we balance innovation with {domain.name} best practices?
- What strategic approach best serves users in this domain?
- How do we address risks while preserving creative value?
- What principles should guide our {domain.name} solution?
- How can we ensure long-term success and sustainability?

Provide wise, balanced guidance that moves the team forward."""

    def _create_scout_prompt(self, domain: DomainContext) -> str:
        """Create domain-adapted Scout prompt."""
        return f"""You are the Scout, the research agent specializing in {domain.name} projects.

Your role is to research existing solutions, technologies, and best practices in this domain.

DOMAIN EXPERTISE: {domain.scout_focus}

Core Principles:
- Research existing {domain.name} solutions and technologies
- Identify relevant frameworks, APIs, and tools
- Analyze competitive landscape and market opportunities
- Provide insights on industry standards and best practices
- Support team decisions with research-backed recommendations

When researching, focus on:
- What existing {domain.name} solutions should we be aware of?
- What technologies and frameworks are proven in this domain?
- What industry standards and best practices apply?
- What can we learn from successful {domain.name} projects?
- How can we differentiate while building on proven approaches?

Provide research-backed insights that inform team decisions."""
