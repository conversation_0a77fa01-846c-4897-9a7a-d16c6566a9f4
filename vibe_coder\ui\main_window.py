"""
Main window for the Vibe Coder GUI interface.
"""

import sys
import logging
from typing import Optional
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QTextEdit, QLineEdit, QPushButton, QLabel,
                             QSplitter, QGroupBox, QProgressBar, QTabWidget)
from PyQt5.QtCore import QThread, pyqtSlot
from PyQt5.QtGui import QFont

from vibe_coder.main import AgentBackend

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """
    Main window for the Vibe Coder application.
    Provides a simple interface for interacting with the agent system.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the main window.
        
        Args:
            config_path: Optional path to configuration file
        """
        super().__init__()
        self.config_path = config_path
        self.agent_backend = None
        self.backend_thread = None
        
        self.init_ui()
        self.setup_backend()
        
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("Vibe Coder - AI Coding Assistant")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Create tab widget for different modes
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Strategic brainstorming tab (FIRST - primary workflow)
        brainstorm_tab = self.create_brainstorming_tab()
        self.tab_widget.addTab(brainstorm_tab, "🧠 Concept Brainstorming")

        # Project development tab (SECOND - after brainstorming)
        main_tab = self.create_main_project_tab()
        self.tab_widget.addTab(main_tab, "🚀 Project Development")
        
        # Status bar
        self.statusBar().showMessage("Initializing Vibe Coder...")
        
        logger.info("UI initialized")

    def create_main_project_tab(self) -> QWidget:
        """Create the main project development tab."""
        tab = QWidget()
        layout = QHBoxLayout(tab)

        # Create splitter for resizable panels
        splitter = QSplitter()
        layout.addWidget(splitter)

        # Left panel - Input and controls
        left_panel = self.create_input_panel()
        splitter.addWidget(left_panel)

        # Right panel - Output and logs
        right_panel = self.create_output_panel()
        splitter.addWidget(right_panel)

        # Set splitter proportions
        splitter.setSizes([400, 800])

        return tab

    def create_brainstorming_tab(self) -> QWidget:
        """Create the strategic brainstorming tab."""
        try:
            from vibe_coder.ui.brainstorming_panel import StrategicBrainstormingPanel

            brainstorm_panel = StrategicBrainstormingPanel()

            # Connect signals (these would be connected to the backend)
            brainstorm_panel.agent_consultation_requested.connect(self.handle_agent_consultation)
            brainstorm_panel.team_brainstorm_requested.connect(self.handle_team_brainstorm)
            brainstorm_panel.project_transfer_requested.connect(self.handle_project_transfer)

            # Store reference for response handling
            self.brainstorm_panel = brainstorm_panel

            return brainstorm_panel

        except ImportError as e:
            logger.error(f"Failed to import brainstorming panel: {e}")
            # Fallback to simple widget
            fallback = QWidget()
            layout = QVBoxLayout(fallback)
            label = QLabel("🧠 Strategic Brainstorming\n\nComing soon! This feature will allow you to brainstorm directly with our strategic agents.")
            label.setStyleSheet("color: #666; font-size: 14pt; text-align: center; padding: 50px;")
            layout.addWidget(label)
            return fallback

    @pyqtSlot(str, str, dict)
    def handle_agent_consultation(self, agent_type: str, message: str, context: dict):
        """Handle agent consultation request."""
        logger.info(f"Agent consultation requested: {agent_type} - {message}")

        # Forward to backend
        if self.agent_backend:
            self.agent_backend.handle_agent_consultation(agent_type, message, context)
        else:
            self.log_output.append("ERROR: Backend not available for consultation")

    @pyqtSlot(str, dict)
    def handle_team_brainstorm(self, topic: str, context: dict):
        """Handle team brainstorm request."""
        logger.info(f"Team brainstorm requested: {topic}")

        # Forward to backend with brainstorming panel reference
        if self.agent_backend:
            # Store reference to brainstorming panel for real-time updates
            if hasattr(self, 'brainstorm_panel'):
                self.agent_backend.brainstorm_panel = self.brainstorm_panel
            self.agent_backend.handle_team_brainstorm(topic, context)
        else:
            self.log_output.append("ERROR: Backend not available for brainstorming")

    @pyqtSlot(str, str, str)
    def on_consultation_response(self, session_id: str, agent_type: str, response: str):
        """Handle consultation response from backend."""
        try:
            # Get the brainstorming panel
            if hasattr(self, 'brainstorm_panel'):
                # For individual agent responses
                if hasattr(self.brainstorm_panel, 'add_agent_response'):
                    self.brainstorm_panel.add_agent_response(agent_type, response)

                # For team responses
                agent_name = self._get_agent_display_name(agent_type)
                if hasattr(self.brainstorm_panel, 'add_team_response'):
                    self.brainstorm_panel.add_team_response(agent_name, response)

            logger.info(f"Consultation response delivered: {agent_type}")

        except Exception as e:
            logger.error(f"Failed to deliver consultation response: {e}")
            self.log_output.append(f"ERROR: Failed to display consultation response: {str(e)}")

    def _get_agent_display_name(self, agent_type: str) -> str:
        """Get display name for agent type."""
        names = {
            "muse": "Muse",
            "oracle": "Oracle",
            "wisdom": "Wisdom Layer",
            "scout": "Scout"
        }
        return names.get(agent_type.lower(), agent_type.title())

    @pyqtSlot(dict)
    def handle_project_transfer(self, brainstorm_summary: dict):
        """Handle transfer of brainstorm results to project development."""
        try:
            # Switch to project development tab (now second tab)
            self.tab_widget.setCurrentIndex(1)

            # Fill in the goal and description
            goal = brainstorm_summary.get("goal", "")
            description = brainstorm_summary.get("description", "")

            if goal:
                self.goal_input.setPlainText(goal)

            if description:
                self.description_input.setPlainText(description)

            # Store brainstorming memory for task reflection
            brainstorm_memory = brainstorm_summary.get("brainstorm_memory", {})
            if brainstorm_memory and self.agent_backend:
                # Store in backend for task reflection
                if hasattr(self.agent_backend, 'store_brainstorm_memory'):
                    self.agent_backend.store_brainstorm_memory(brainstorm_memory)

            # Log the transfer with insights
            agents_participated = brainstorm_summary.get("agents_participated", [])
            key_insights = brainstorm_summary.get("key_insights", [])
            decisions_made = brainstorm_summary.get("decisions_made", [])

            self.log_output.append(f"📋 Transferred brainstorm results to project development")
            self.log_output.append(f"   🤖 Agents participated: {', '.join(agents_participated)}")
            self.log_output.append(f"   💬 Conversation: {brainstorm_summary.get('conversation_length', 0)} messages")
            self.log_output.append(f"   💡 Key insights: {len(key_insights)} captured")
            self.log_output.append(f"   ✅ Decisions made: {len(decisions_made)} recorded")
            self.log_output.append("   📝 Brainstorm memory stored for task planning reflection")
            self.log_output.append("   👀 Review the goal and description, then click 'Start Project' when ready.")

            logger.info("Brainstorm results transferred to project development")

        except Exception as e:
            logger.error(f"Failed to transfer brainstorm results: {e}")
            self.log_output.append(f"ERROR: Failed to transfer brainstorm results: {str(e)}")

    def create_input_panel(self) -> QWidget:
        """Create the input panel with controls."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Goal input section
        goal_group = QGroupBox("Project Goal")
        goal_layout = QVBoxLayout(goal_group)
        
        self.goal_input = QTextEdit()
        self.goal_input.setPlaceholderText("Enter your project goal here...\n\nExample: Create a simple web scraper that extracts product prices from an e-commerce website")
        self.goal_input.setMaximumHeight(150)
        goal_layout.addWidget(self.goal_input)
        
        # Description input
        desc_label = QLabel("Description (optional):")
        goal_layout.addWidget(desc_label)
        
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("Additional details about your project...")
        self.description_input.setMaximumHeight(100)
        goal_layout.addWidget(self.description_input)
        
        layout.addWidget(goal_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("Start Project")
        self.start_button.clicked.connect(self.start_project)
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("Stop")
        self.stop_button.clicked.connect(self.stop_project)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 10px; }")
        button_layout.addWidget(self.stop_button)
        
        layout.addLayout(button_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # System status
        status_group = QGroupBox("System Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("Status: Initializing...")
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        # Add stretch to push everything to the top
        layout.addStretch()
        
        return panel
    
    def create_output_panel(self) -> QWidget:
        """Create the output panel with logs and results."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Log output
        log_group = QGroupBox("System Log")
        log_layout = QVBoxLayout(log_group)
        
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setFont(QFont("Consolas", 9))
        self.log_output.setStyleSheet("QTextEdit { background-color: #2b2b2b; color: #ffffff; }")
        log_layout.addWidget(self.log_output)
        
        layout.addWidget(log_group)
        
        # Results output
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_output = QTextEdit()
        self.results_output.setReadOnly(True)
        self.results_output.setFont(QFont("Consolas", 9))
        results_layout.addWidget(self.results_output)
        
        layout.addWidget(results_group)
        
        return panel
    
    def setup_backend(self):
        """Setup the agent backend in a separate thread."""
        try:
            # Create backend
            self.agent_backend = AgentBackend(self.config_path)
            
            # Create thread
            self.backend_thread = QThread()
            self.agent_backend.moveToThread(self.backend_thread)
            
            # Connect signals
            self.agent_backend.log_signal.connect(self.on_log_message)
            self.agent_backend.status_signal.connect(self.on_status_update)
            self.agent_backend.result_signal.connect(self.on_result_received)

            # Connect consultation signals
            self.agent_backend.consultation_response_ready.connect(self.on_consultation_response)
            
            # Connect thread signals
            self.backend_thread.started.connect(self.agent_backend.run)
            
            # Start the backend thread
            self.backend_thread.start()
            
            logger.info("Backend setup complete")
            
        except Exception as e:
            logger.error(f"Failed to setup backend: {e}")
            self.log_output.append(f"ERROR: Failed to setup backend: {str(e)}")
    
    @pyqtSlot(str)
    def on_log_message(self, message: str):
        """Handle log messages from the backend."""
        self.log_output.append(message)
        self.log_output.ensureCursorVisible()
    
    @pyqtSlot(str)
    def on_status_update(self, status: str):
        """Handle status updates from the backend."""
        self.status_label.setText(f"Status: {status}")
        self.statusBar().showMessage(status)
        
        if status == "Ready":
            self.start_button.setEnabled(True)
            self.progress_bar.setVisible(False)
        elif status == "Error":
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.progress_bar.setVisible(False)
    
    @pyqtSlot(dict)
    def on_result_received(self, result: dict):
        """Handle results from the backend."""
        self.results_output.clear()
        
        if result.get("success"):
            self.results_output.append("✅ Project completed successfully!\n")
            
            results = result.get("results", [])
            self.results_output.append(f"Tasks completed: {len(results)}\n")
            
            for i, task_result in enumerate(results, 1):
                self.results_output.append(f"Task {i}:")
                self.results_output.append(f"  Success: {task_result.get('success', False)}")
                self.results_output.append(f"  Tool used: {task_result.get('tool_used', 'N/A')}")
                self.results_output.append(f"  Result: {task_result.get('result', 'N/A')[:200]}...")
                self.results_output.append("")
        else:
            self.results_output.append("❌ Project failed!\n")
            self.results_output.append(f"Error: {result.get('error', 'Unknown error')}")
        
        # Re-enable controls
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
    
    def start_project(self):
        """Start a new project."""
        goal = self.goal_input.toPlainText().strip()
        if not goal:
            self.log_output.append("ERROR: Please enter a project goal")
            return
        
        description = self.description_input.toPlainText().strip()
        
        # Update UI state
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.results_output.clear()
        
        # Submit goal to backend via signal (ensures it runs in backend thread)
        if self.agent_backend:
            self.agent_backend.goal_submitted.emit(goal, description)
        else:
            self.log_output.append("ERROR: Backend not available")
    
    def stop_project(self):
        """Stop the current project."""
        self.log_output.append("🛑 Stopping project execution...")

        # Signal backend to stop via signal (ensures thread safety)
        if self.agent_backend:
            self.agent_backend.stop_requested.emit()

        # Reset UI state
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        self.log_output.append("✅ Project execution stopped.")
    
    def closeEvent(self, event):
        """Handle window close event."""
        if self.backend_thread and self.backend_thread.isRunning():
            self.backend_thread.quit()
            self.backend_thread.wait()
        event.accept()


def launch_gui(config_path: Optional[str] = None):
    """Launch the GUI application."""
    app = QApplication(sys.argv)
    app.setApplicationName("Vibe Coder")
    
    window = MainWindow(config_path)
    window.show()
    
    sys.exit(app.exec_())
