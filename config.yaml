agent_assignments:
  coder_agent: deepseek
  meta_agent: openrouter
  task_decomposer: openrouter
  web_search: tavily
  # Brainstorming agents
  muse_agent: openrouter
  oracle_agent: deepseek
  wisdom_layer: openrouter
  open_source_scout: openrouter
llm_providers:
  anthropic:
    api_key: ${ANTHROPIC_API_KEY}
    base_url: https://api.anthropic.com
    coder_model_name: claude-3-sonnet-20240229
    supervisor_model_name: claude-3-opus-20240229
  deepseek:
    api_key: ${DEEPSEEK_API_KEY}
    base_url: https://api.deepseek.com/v1
    coder_model_name: deepseek-reasoner
    supervisor_model_name: deepseek-reasoner
  default_provider: openrouter
  openai:
    api_key: ${OPENAI_API_KEY}
    base_url: https://api.openai.com/v1
    coder_model_name: gpt-4-turbo
    supervisor_model_name: gpt-4o
  openrouter:
    api_key: ${OPENROUTER_API_KEY}
    base_url: https://openrouter.ai/api/v1
    coder_model_name: google/gemini-2.5-pro-preview
    supervisor_model_name: google/gemini-2.5-pro-preview
  tavily:
    api_key: ${TAVILY_API_KEY}
paths:
  action_ledger_db: ./data/action_ledger.db
  lessons_learned_db: ./data/lessons_learned.json
  log_file: ./logs/vibe_coder.log
  tree_sitter_grammars: ./vendor/tree-sitter-grammars
  tree_sitter_library: ./build/my-languages.dll
  vector_store_path: ./data/vector_store
settings:
  enable_reflection: true
  enable_validation: true
  llm_timeout: 60
  log_level: INFO
  max_task_retries: 3
