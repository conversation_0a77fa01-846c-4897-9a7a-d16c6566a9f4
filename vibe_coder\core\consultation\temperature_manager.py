"""
Dynamic temperature management for brainstorming agents.

This module provides functionality to adjust agent temperature based on
conversation convergence and other contextual factors.
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TemperatureConfig:
    """Configuration for dynamic temperature adjustment."""
    base_temperature: float = 0.8
    min_temperature: float = 0.3
    max_temperature: float = 0.9
    convergence_sensitivity: float = 1.0  # How much convergence affects temperature


class ConvergenceAnalyzer:
    """Analyzes conversation convergence to inform temperature adjustments."""
    
    def __init__(self):
        """Initialize convergence analyzer."""
        self.convergence_indicators = {
            "agreement_phrases": [
                "i agree", "exactly", "that's right", "good point", "yes",
                "absolutely", "definitely", "precisely", "correct", "true"
            ],
            "building_phrases": [
                "building on", "adding to", "expanding on", "following up",
                "to add", "also", "furthermore", "additionally", "similarly"
            ],
            "consensus_phrases": [
                "we should", "let's", "our approach", "we need", "we could",
                "the solution", "our goal", "we agree", "consensus", "together"
            ]
        }
        
        logger.info("ConvergenceAnalyzer initialized")
    
    def calculate_convergence_score(self, messages: list, shared_context: Dict[str, Any] = None) -> float:
        """
        Calculate convergence score based on conversation analysis.
        
        Args:
            messages: List of recent conversation messages
            shared_context: Optional shared context with convergence metrics
            
        Returns:
            Convergence score from 0.0 (divergent) to 1.0 (fully converged)
        """
        if not messages:
            return 0.0
        
        # Start with shared context convergence if available
        base_score = 0.0
        if shared_context:
            convergence_state = shared_context.get("convergence_state", "divergent")
            state_scores = {
                "divergent": 0.0,
                "early": 0.2,
                "working": 0.5,
                "strong": 0.8,
                "complete": 1.0
            }
            base_score = state_scores.get(convergence_state, 0.0)
        
        # Analyze recent messages for convergence indicators
        message_score = self._analyze_message_convergence(messages)
        
        # Combine scores (weighted average)
        final_score = (base_score * 0.6) + (message_score * 0.4)
        
        logger.debug(f"Convergence score: {final_score:.2f} (base: {base_score:.2f}, messages: {message_score:.2f})")
        return min(1.0, max(0.0, final_score))
    
    def _analyze_message_convergence(self, messages: list) -> float:
        """Analyze messages for convergence indicators."""
        if not messages:
            return 0.0
        
        total_indicators = 0
        total_words = 0
        
        for msg in messages[-8:]:  # Analyze last 8 messages
            if isinstance(msg, dict) and 'message' in msg:
                text = msg['message'].lower()
                words = text.split()
                total_words += len(words)
                
                # Count convergence indicators
                for category, phrases in self.convergence_indicators.items():
                    for phrase in phrases:
                        if phrase in text:
                            total_indicators += 1
        
        if total_words == 0:
            return 0.0
        
        # Calculate convergence ratio
        convergence_ratio = total_indicators / max(total_words, 1)
        
        # Scale to 0-1 range (empirically tuned)
        score = min(1.0, convergence_ratio * 20)  # Scale factor of 20
        
        return score


class DynamicTemperatureManager:
    """Manages dynamic temperature adjustment for agents."""
    
    def __init__(self):
        """Initialize temperature manager."""
        self.convergence_analyzer = ConvergenceAnalyzer()
        
        # Agent-specific temperature configurations
        self.agent_configs = {
            "muse": TemperatureConfig(
                base_temperature=0.8,
                min_temperature=0.3,
                max_temperature=0.9,
                convergence_sensitivity=1.0
            ),
            "oracle": TemperatureConfig(
                base_temperature=0.1,
                min_temperature=0.05,
                max_temperature=0.2,
                convergence_sensitivity=0.3  # Oracle stays more consistent
            ),
            "wisdom": TemperatureConfig(
                base_temperature=0.3,
                min_temperature=0.2,
                max_temperature=0.4,
                convergence_sensitivity=0.5
            ),
            "scout": TemperatureConfig(
                base_temperature=0.2,
                min_temperature=0.1,
                max_temperature=0.3,
                convergence_sensitivity=0.4
            )
        }
        
        logger.info("DynamicTemperatureManager initialized")
    
    def get_agent_temperature(self, 
                            agent_name: str, 
                            messages: list, 
                            shared_context: Dict[str, Any] = None) -> float:
        """
        Get dynamic temperature for a specific agent.
        
        Args:
            agent_name: Name of the agent (muse, oracle, wisdom, scout)
            messages: Recent conversation messages
            shared_context: Optional shared context with convergence metrics
            
        Returns:
            Calculated temperature for the agent
        """
        config = self.agent_configs.get(agent_name, self.agent_configs["muse"])
        
        # Calculate convergence score
        convergence_score = self.convergence_analyzer.calculate_convergence_score(
            messages, shared_context
        )
        
        # Calculate temperature based on convergence
        temperature = self._calculate_temperature(config, convergence_score)
        
        logger.debug(f"Agent {agent_name} temperature: {temperature:.2f} (convergence: {convergence_score:.2f})")
        return temperature
    
    def _calculate_temperature(self, config: TemperatureConfig, convergence_score: float) -> float:
        """Calculate temperature based on configuration and convergence."""
        # Apply convergence sensitivity
        adjusted_convergence = convergence_score * config.convergence_sensitivity
        
        # Linear interpolation between max and min temperature
        # High convergence = low temperature (more focused)
        # Low convergence = high temperature (more creative)
        temperature_range = config.max_temperature - config.min_temperature
        temperature = config.max_temperature - (adjusted_convergence * temperature_range)
        
        # Ensure within bounds
        temperature = max(config.min_temperature, min(config.max_temperature, temperature))
        
        return temperature
    
    def get_all_agent_temperatures(self, 
                                 messages: list, 
                                 shared_context: Dict[str, Any] = None) -> Dict[str, float]:
        """
        Get temperatures for all agents.
        
        Args:
            messages: Recent conversation messages
            shared_context: Optional shared context with convergence metrics
            
        Returns:
            Dictionary mapping agent names to temperatures
        """
        temperatures = {}
        
        for agent_name in self.agent_configs.keys():
            temperatures[agent_name] = self.get_agent_temperature(
                agent_name, messages, shared_context
            )
        
        return temperatures
    
    def update_agent_config(self, agent_name: str, config: TemperatureConfig):
        """Update temperature configuration for an agent."""
        self.agent_configs[agent_name] = config
        logger.info(f"Updated temperature config for {agent_name}")


# Convenience functions for easy integration
def get_muse_temperature(messages: list, shared_context: Dict[str, Any] = None) -> float:
    """Get dynamic temperature for Muse agent."""
    manager = DynamicTemperatureManager()
    return manager.get_agent_temperature("muse", messages, shared_context)


def get_agent_temperatures(messages: list, shared_context: Dict[str, Any] = None) -> Dict[str, float]:
    """Get dynamic temperatures for all agents."""
    manager = DynamicTemperatureManager()
    return manager.get_all_agent_temperatures(messages, shared_context)
