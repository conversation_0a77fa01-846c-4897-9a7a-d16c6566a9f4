"""
Strategic Consultation Manager for Human-Agent Brainstorming.

This module handles direct consultation and brainstorming sessions
between humans and strategic agents.
"""

import logging
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time
from collections import defaultdict
import re
from queue import Queue, Empty

logger = logging.getLogger(__name__)


@dataclass
class ConvergenceMetrics:
    """Tracks convergence metrics for team brainstorming."""
    semantic_similarity: float = 0.0
    concept_stability: float = 0.0
    participant_agreement: float = 0.0
    solution_specificity: float = 0.0
    user_satisfaction: float = 0.0
    refinement_ratio: float = 0.0

    def overall_score(self) -> float:
        """Calculate weighted convergence score."""
        weights = {
            'semantic_similarity': 0.2,
            'concept_stability': 0.25,
            'participant_agreement': 0.2,
            'solution_specificity': 0.15,
            'user_satisfaction': 0.1,
            'refinement_ratio': 0.1
        }

        return (
            self.semantic_similarity * weights['semantic_similarity'] +
            self.concept_stability * weights['concept_stability'] +
            self.participant_agreement * weights['participant_agreement'] +
            self.solution_specificity * weights['solution_specificity'] +
            self.user_satisfaction * weights['user_satisfaction'] +
            self.refinement_ratio * weights['refinement_ratio']
        )

    def convergence_level(self) -> str:
        """Get convergence level description."""
        score = self.overall_score()
        if score >= 0.9:
            return "complete"
        elif score >= 0.8:
            return "strong"
        elif score >= 0.7:
            return "working"
        elif score >= 0.6:
            return "early"
        else:
            return "divergent"


@dataclass
class SharedContext:
    """Shared context pool for convergent brainstorming."""
    original_goal: str = ""
    conversation_themes: List[str] = field(default_factory=list)
    emerging_consensus: str = ""
    active_tensions: List[str] = field(default_factory=list)
    user_priorities: List[str] = field(default_factory=list)
    convergence_state: str = "divergent"
    next_needed: str = ""
    key_concepts: Dict[str, int] = field(default_factory=dict)  # concept -> frequency
    decisions_made: List[str] = field(default_factory=list)

    def add_concept(self, concept: str):
        """Add or increment concept frequency."""
        concept = concept.lower().strip()
        if concept:
            self.key_concepts[concept] = self.key_concepts.get(concept, 0) + 1

    def get_top_concepts(self, limit: int = 5) -> List[str]:
        """Get most frequently mentioned concepts."""
        return sorted(self.key_concepts.items(), key=lambda x: x[1], reverse=True)[:limit]


@dataclass
class ConsultationSession:
    """Represents a consultation session with an agent."""
    session_id: str
    agent_type: str
    human_participant: str
    start_time: datetime
    messages: List[Dict[str, Any]]
    context: Dict[str, Any]
    status: str = "active"  # active, paused, completed
    shared_context: SharedContext = field(default_factory=SharedContext)
    convergence_metrics: ConvergenceMetrics = field(default_factory=ConvergenceMetrics)

    def add_message(self, sender: str, message: str, sender_type: str):
        """Add a message to the session."""
        self.messages.append({
            "timestamp": datetime.now(),
            "sender": sender,
            "message": message,
            "sender_type": sender_type
        })

        # Extract concepts from message
        self._extract_concepts(message)

    def _extract_concepts(self, message: str):
        """Extract key concepts from message."""
        # Simple concept extraction - could be enhanced with NLP
        words = re.findall(r'\b[a-zA-Z]{3,}\b', message.lower())
        # Filter out common words
        stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'what', 'when', 'with', 'have', 'this', 'will', 'your', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'}

        concepts = [word for word in words if word not in stop_words and len(word) > 3]
        for concept in concepts:
            self.shared_context.add_concept(concept)


class ConvergenceDetector:
    """Detects convergence in team brainstorming sessions."""

    def __init__(self):
        self.concept_history = defaultdict(list)
        self.agreement_patterns = []

    def analyze_convergence(self, session: ConsultationSession) -> ConvergenceMetrics:
        """Analyze convergence metrics for a session."""
        metrics = ConvergenceMetrics()

        if len(session.messages) < 3:
            return metrics

        # Analyze recent messages (last 6)
        recent_messages = session.messages[-6:]

        # Calculate semantic similarity
        metrics.semantic_similarity = self._calculate_semantic_similarity(recent_messages)

        # Calculate concept stability
        metrics.concept_stability = self._calculate_concept_stability(session)

        # Calculate participant agreement
        metrics.participant_agreement = self._calculate_participant_agreement(recent_messages)

        # Calculate solution specificity
        metrics.solution_specificity = self._calculate_solution_specificity(recent_messages)

        # Calculate user satisfaction (based on user responses)
        metrics.user_satisfaction = self._calculate_user_satisfaction(recent_messages)

        # Calculate refinement ratio
        metrics.refinement_ratio = self._calculate_refinement_ratio(recent_messages)

        return metrics

    def _calculate_semantic_similarity(self, messages: List[Dict]) -> float:
        """Calculate semantic similarity between recent messages."""
        if len(messages) < 2:
            return 0.0

        # Simple keyword overlap analysis
        message_words = []
        for msg in messages:
            words = set(re.findall(r'\b[a-zA-Z]{3,}\b', msg['message'].lower()))
            message_words.append(words)

        if len(message_words) < 2:
            return 0.0

        # Calculate average pairwise similarity
        similarities = []
        for i in range(len(message_words)):
            for j in range(i + 1, len(message_words)):
                intersection = len(message_words[i] & message_words[j])
                union = len(message_words[i] | message_words[j])
                if union > 0:
                    similarities.append(intersection / union)

        return sum(similarities) / len(similarities) if similarities else 0.0

    def _calculate_concept_stability(self, session: ConsultationSession) -> float:
        """Calculate how stable key concepts are becoming."""
        top_concepts = session.shared_context.get_top_concepts(5)
        if not top_concepts:
            return 0.0

        # Check if top concepts are being reinforced
        total_mentions = sum(count for _, count in top_concepts)
        if total_mentions < 3:
            return 0.0

        # Higher stability if top concepts dominate
        top_3_mentions = sum(count for _, count in top_concepts[:3])
        return min(1.0, top_3_mentions / total_mentions)

    def _calculate_participant_agreement(self, messages: List[Dict]) -> float:
        """Calculate level of agreement vs disagreement."""
        agreement_words = {'agree', 'yes', 'exactly', 'right', 'good', 'great', 'perfect', 'love', 'like', 'build', 'expand', 'enhance'}
        disagreement_words = {'no', 'but', 'however', 'disagree', 'wrong', 'issue', 'problem', 'concern', 'risk'}

        agreement_count = 0
        disagreement_count = 0

        for msg in messages:
            text = msg['message'].lower()
            agreement_count += sum(1 for word in agreement_words if word in text)
            disagreement_count += sum(1 for word in disagreement_words if word in text)

        total = agreement_count + disagreement_count
        if total == 0:
            return 0.5  # Neutral

        return agreement_count / total

    def _calculate_solution_specificity(self, messages: List[Dict]) -> float:
        """Calculate how specific/concrete the solutions are becoming."""
        specific_words = {'implement', 'code', 'function', 'class', 'method', 'algorithm', 'database', 'api', 'framework', 'library', 'tool', 'step', 'process', 'workflow'}
        abstract_words = {'idea', 'concept', 'think', 'maybe', 'perhaps', 'could', 'might', 'possibly', 'generally', 'overall'}

        specific_count = 0
        abstract_count = 0

        for msg in messages:
            text = msg['message'].lower()
            specific_count += sum(1 for word in specific_words if word in text)
            abstract_count += sum(1 for word in abstract_words if word in text)

        total = specific_count + abstract_count
        if total == 0:
            return 0.0

        return specific_count / total

    def _calculate_user_satisfaction(self, messages: List[Dict]) -> float:
        """Calculate user satisfaction based on their responses."""
        user_messages = [msg for msg in messages if msg.get('sender_type') == 'human']
        if not user_messages:
            return 0.5

        positive_words = {'great', 'good', 'excellent', 'perfect', 'love', 'like', 'yes', 'exactly', 'right'}
        negative_words = {'no', 'wrong', 'bad', 'terrible', 'hate', 'dislike', 'issue', 'problem'}

        positive_count = 0
        negative_count = 0

        for msg in user_messages:
            text = msg['message'].lower()
            positive_count += sum(1 for word in positive_words if word in text)
            negative_count += sum(1 for word in negative_words if word in text)

        total = positive_count + negative_count
        if total == 0:
            return 0.7  # Assume neutral-positive

        return positive_count / total

    def _calculate_refinement_ratio(self, messages: List[Dict]) -> float:
        """Calculate ratio of refinement vs new ideas."""
        refinement_words = {'improve', 'enhance', 'refine', 'adjust', 'modify', 'tweak', 'optimize', 'better', 'fix'}
        new_idea_words = {'new', 'different', 'alternative', 'another', 'instead', 'what about', 'how about'}

        refinement_count = 0
        new_idea_count = 0

        for msg in messages:
            text = msg['message'].lower()
            refinement_count += sum(1 for word in refinement_words if word in text)
            new_idea_count += sum(1 for phrase in new_idea_words if phrase in text)

        total = refinement_count + new_idea_count
        if total == 0:
            return 0.0

        return refinement_count / total


class StrategicConsultationManager:
    """
    Manages strategic consultation sessions between humans and AI agents.
    Supports convergent brainstorming with parallel processing and real-time updates.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the consultation manager."""
        self.config = config
        self.active_sessions = {}
        self.session_history = []

        # Initialize agents (these would be the same agents used in strategic debate)
        self.muse_agent = None
        self.oracle_agent = None
        self.wisdom_layer = None
        self.scout_agent = None  # OpenSourceScout for research

        # Convergence detection
        self.convergence_detector = ConvergenceDetector()

        # Threading for parallel processing
        self.executor = ThreadPoolExecutor(max_workers=4)

        # Response callbacks for real-time updates
        self.response_callbacks = {}

        # Context update queue for real-time user input
        self.context_update_queue = Queue()

        logger.info("StrategicConsultationManager initialized")
    
    def initialize_agents(self, muse_agent, oracle_agent, wisdom_layer, scout_agent=None):
        """Initialize the strategic agents for consultation."""
        self.muse_agent = muse_agent
        self.oracle_agent = oracle_agent
        self.wisdom_layer = wisdom_layer
        self.scout_agent = scout_agent
        logger.info("Strategic agents initialized for consultation")
    
    def start_consultation(self, 
                          agent_type: str, 
                          human_participant: str, 
                          initial_message: str,
                          context: Dict[str, Any]) -> str:
        """
        Start a consultation session with a specific agent.
        
        Args:
            agent_type: Type of agent (muse, oracle, wisdom)
            human_participant: Name/ID of human participant
            initial_message: Initial message from human
            context: Current project/session context
            
        Returns:
            Session ID for the consultation
        """
        try:
            session_id = f"{agent_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create consultation session
            session = ConsultationSession(
                session_id=session_id,
                agent_type=agent_type,
                human_participant=human_participant,
                start_time=datetime.now(),
                messages=[],
                context=context
            )
            
            # Add initial human message
            session.add_message(human_participant, initial_message, "human")
            
            # Store session
            self.active_sessions[session_id] = session
            
            logger.info(f"Started consultation session {session_id} with {agent_type}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to start consultation: {e}")
            return ""
    
    def get_agent_response(self, 
                          session_id: str, 
                          message: str) -> Dict[str, Any]:
        """
        Get response from agent for a consultation message.
        
        Args:
            session_id: ID of the consultation session
            message: Message from human
            
        Returns:
            Dictionary containing agent response and metadata
        """
        try:
            if session_id not in self.active_sessions:
                return {"success": False, "error": "Session not found"}
            
            session = self.active_sessions[session_id]
            agent_type = session.agent_type
            
            # Add human message to session
            session.add_message(session.human_participant, message, "human")
            
            # Prepare consultation context
            consultation_context = self._prepare_consultation_context(session)
            
            # Get response from appropriate agent
            if agent_type == "muse" and self.muse_agent:
                response = self._consult_muse(message, consultation_context)
            elif agent_type == "oracle" and self.oracle_agent:
                response = self._consult_oracle(message, consultation_context)
            elif agent_type == "wisdom" and self.wisdom_layer:
                response = self._consult_wisdom(message, consultation_context)
            elif agent_type == "scout" and self.scout_agent:
                response = self._consult_scout(message, consultation_context)
            else:
                return {"success": False, "error": f"Agent {agent_type} not available"}
            
            # Add agent response to session
            agent_name = self._get_agent_name(agent_type)
            session.add_message(agent_name, response, agent_type)
            
            return {
                "success": True,
                "response": response,
                "agent_type": agent_type,
                "agent_name": agent_name,
                "session_id": session_id
            }
            
        except Exception as e:
            logger.error(f"Failed to get agent response: {e}")
            return {"success": False, "error": str(e)}
    
    def set_response_callback(self, session_id: str, callback: Callable):
        """Set callback for real-time response updates."""
        self.response_callbacks[session_id] = callback

    def add_user_input_to_context(self, session_id: str, message: str):
        """Add user input to ongoing context (thread-safe)."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.add_message("User", message, "human")

            # Update shared context
            self._update_shared_context(session, message)

            # Notify any ongoing agent processes
            self.context_update_queue.put({
                'session_id': session_id,
                'message': message,
                'timestamp': datetime.now()
            })

    def start_team_brainstorm_parallel(self,
                                     message: str,
                                     human_participant: str,
                                     context: Dict[str, Any],
                                     response_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Start parallel team brainstorming with convergent intelligence.

        Args:
            message: Current message from human
            human_participant: Name/ID of human participant
            context: Current project/session context
            response_callback: Callback for real-time response updates

        Returns:
            Dictionary containing session info and initial status
        """
        try:
            logger.info(f"Starting parallel team brainstorm: {message}")

            # Create session
            session_id = f"team_parallel_{datetime.now().strftime('%H%M%S')}"
            session = self._create_or_update_session(session_id, message, human_participant, context)

            # Set response callback
            if response_callback:
                self.set_response_callback(session_id, response_callback)

            # Start parallel agent consultations
            self._start_parallel_consultations(session)

            return {
                "success": True,
                "session_id": session_id,
                "message": message,
                "status": "processing",
                "convergence_level": "starting"
            }

        except Exception as e:
            logger.error(f"Failed to start parallel team brainstorm: {e}")
            return {"success": False, "error": str(e)}

    def start_team_brainstorm(self,
                             message: str,
                             human_participant: str,
                             context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle team brainstorming message (ongoing conversation).

        Args:
            message: Current message from human
            human_participant: Name/ID of human participant
            context: Current project/session context

        Returns:
            Dictionary containing team responses
        """
        try:
            logger.info(f"Starting team brainstorm with message: {message}")
            logger.info(f"Available agents - Muse: {self.muse_agent is not None}, Oracle: {self.oracle_agent is not None}, Wisdom: {self.wisdom_layer is not None}, Scout: {self.scout_agent is not None}")

            # Check if this is an ongoing session
            session_active = context.get("session_active", False)
            conversation_history = context.get("conversation_history", [])

            if session_active and conversation_history:
                session_id = f"team_ongoing_{datetime.now().strftime('%H%M%S')}"
            else:
                session_id = f"team_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            logger.info(f"Created session ID: {session_id}")

            # Create or continue team session
            session = ConsultationSession(
                session_id=session_id,
                agent_type="team",
                human_participant=human_participant,
                start_time=datetime.now(),
                messages=[],
                context=context
            )

            # Add conversation history if available
            for msg in conversation_history:
                session.add_message(
                    msg.get("sender", "Unknown"),
                    msg.get("message", ""),
                    msg.get("sender_type", "unknown")
                )

            # Add current message
            session.add_message(human_participant, message, "human")

            # Get responses from all agents
            team_responses = []
            logger.info("Starting agent consultations...")

            # Muse response (creative perspective)
            if self.muse_agent:
                logger.info("Consulting Muse agent...")
                muse_context = self._prepare_consultation_context(session)
                if session_active:
                    muse_prompt = f"Continuing our brainstorm conversation, responding to: {message}"
                else:
                    muse_prompt = f"Let's brainstorm about: {message}"

                muse_response = self._consult_muse(muse_prompt, muse_context)
                logger.info(f"Muse response received: {len(muse_response)} characters")
                team_responses.append({
                    "agent": "Muse",
                    "response": muse_response,
                    "perspective": "creative"
                })
                session.add_message("Muse", muse_response, "muse")
            else:
                logger.warning("Muse agent not available for consultation")
            
            # Oracle response (analytical perspective)
            if self.oracle_agent:
                oracle_context = self._prepare_consultation_context(session)
                if session_active:
                    oracle_prompt = f"Analyzing the conversation, responding to: {message}"
                else:
                    oracle_prompt = f"Analyze this brainstorming topic: {message}"

                oracle_response = self._consult_oracle(oracle_prompt, oracle_context)
                team_responses.append({
                    "agent": "Oracle",
                    "response": oracle_response,
                    "perspective": "analytical"
                })
                session.add_message("Oracle", oracle_response, "oracle")
            
            # Wisdom Layer response (strategic perspective)
            if self.wisdom_layer:
                wisdom_context = self._prepare_consultation_context(session)
                if session_active:
                    wisdom_prompt = f"Providing strategic perspective on: {message}"
                else:
                    wisdom_prompt = f"Provide strategic guidance on: {message}"

                wisdom_response = self._consult_wisdom(wisdom_prompt, wisdom_context)
                team_responses.append({
                    "agent": "Wisdom Layer",
                    "response": wisdom_response,
                    "perspective": "strategic"
                })
                session.add_message("Wisdom Layer", wisdom_response, "wisdom")

            # Scout response (research perspective) - only for new topics or research requests
            if self.scout_agent and (not session_active or "research" in message.lower() or "existing" in message.lower()):
                scout_context = self._prepare_consultation_context(session)
                if session_active:
                    scout_prompt = f"Research insights related to: {message}"
                else:
                    scout_prompt = f"Research existing solutions and implementations for: {message}"

                scout_response = self._consult_scout(scout_prompt, scout_context)
                team_responses.append({
                    "agent": "Scout",
                    "response": scout_response,
                    "perspective": "research"
                })
                session.add_message("Scout", scout_response, "scout")
            
            # Store session
            self.active_sessions[session_id] = session
            
            return {
                "success": True,
                "session_id": session_id,
                "message": message,
                "session_active": session_active,
                "responses": team_responses
            }
            
        except Exception as e:
            logger.error(f"Failed to start team brainstorm: {e}")
            return {"success": False, "error": str(e)}

    def _create_or_update_session(self, session_id: str, message: str, human_participant: str, context: Dict[str, Any]) -> ConsultationSession:
        """Create or update a consultation session."""
        session_active = context.get("session_active", False)
        conversation_history = context.get("conversation_history", [])

        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
        else:
            session = ConsultationSession(
                session_id=session_id,
                agent_type="team",
                human_participant=human_participant,
                start_time=datetime.now(),
                messages=[],
                context=context
            )

            # Add conversation history if available
            for msg in conversation_history:
                session.add_message(
                    msg.get("sender", "Unknown"),
                    msg.get("message", ""),
                    msg.get("sender_type", "unknown")
                )

        # Add current message
        session.add_message(human_participant, message, "human")

        # Update shared context
        self._update_shared_context(session, message)

        # Store session
        self.active_sessions[session_id] = session

        return session

    def _update_shared_context(self, session: ConsultationSession, message: str):
        """Update shared context with new information."""
        # Extract and update themes, priorities, etc.
        if not session.shared_context.original_goal:
            session.shared_context.original_goal = message

        # Simple theme extraction (could be enhanced)
        themes = re.findall(r'\b(?:ai|game|web|app|tool|system|platform|service)\b', message.lower())
        for theme in themes:
            if theme not in session.shared_context.conversation_themes:
                session.shared_context.conversation_themes.append(theme)

    def _start_parallel_consultations(self, session: ConsultationSession):
        """Start parallel agent consultations with convergent context."""
        agents_to_consult = []

        if self.muse_agent:
            agents_to_consult.append(('muse', self.muse_agent))
        if self.oracle_agent:
            agents_to_consult.append(('oracle', self.oracle_agent))
        if self.wisdom_layer:
            agents_to_consult.append(('wisdom', self.wisdom_layer))
        if self.scout_agent:
            agents_to_consult.append(('scout', self.scout_agent))

        # Submit parallel consultation tasks
        futures = {}
        for agent_type, agent in agents_to_consult:
            future = self.executor.submit(
                self._consult_agent_with_convergent_context,
                agent_type,
                agent,
                session
            )
            futures[future] = agent_type

        # Process responses as they complete
        threading.Thread(
            target=self._process_parallel_responses,
            args=(session, futures),
            daemon=True
        ).start()

    def _consult_agent_with_convergent_context(self, agent_type: str, agent, session: ConsultationSession) -> Dict[str, Any]:
        """Consult an agent with full convergent context."""
        try:
            # Prepare convergent context
            context = self._prepare_convergent_context(session)

            # Get the latest user message
            user_messages = [msg for msg in session.messages if msg.get('sender_type') == 'human']
            latest_message = user_messages[-1]['message'] if user_messages else ""

            # Consult agent based on type
            if agent_type == 'muse':
                response = self._consult_muse_convergent(latest_message, context, session)
            elif agent_type == 'oracle':
                response = self._consult_oracle_convergent(latest_message, context, session)
            elif agent_type == 'wisdom':
                response = self._consult_wisdom_convergent(latest_message, context, session)
            elif agent_type == 'scout':
                response = self._consult_scout_convergent(latest_message, context, session)
            else:
                response = f"Agent {agent_type} consultation not implemented"

            return {
                'agent_type': agent_type,
                'agent_name': self._get_agent_name(agent_type),
                'response': response,
                'timestamp': datetime.now(),
                'success': True
            }

        except Exception as e:
            logger.error(f"Agent {agent_type} consultation failed: {e}")
            return {
                'agent_type': agent_type,
                'agent_name': self._get_agent_name(agent_type),
                'response': f"I'm having trouble processing this right now. Could you rephrase?",
                'timestamp': datetime.now(),
                'success': False,
                'error': str(e)
            }

    def _process_parallel_responses(self, session: ConsultationSession, futures: Dict):
        """Process agent responses as they complete and check for convergence."""
        callback = self.response_callbacks.get(session.session_id)

        for future in as_completed(futures):
            agent_type = futures[future]
            try:
                result = future.result()

                # Add response to session
                session.add_message(
                    result['agent_name'],
                    result['response'],
                    agent_type
                )

                # Send real-time update
                if callback:
                    callback(result)

                # Check for convergence after each response
                metrics = self.convergence_detector.analyze_convergence(session)
                session.convergence_metrics = metrics

                # Update convergence state
                convergence_level = metrics.convergence_level()
                session.shared_context.convergence_state = convergence_level

                # Send convergence update
                if callback:
                    callback({
                        'type': 'convergence_update',
                        'convergence_level': convergence_level,
                        'convergence_score': metrics.overall_score(),
                        'metrics': {
                            'semantic_similarity': metrics.semantic_similarity,
                            'concept_stability': metrics.concept_stability,
                            'participant_agreement': metrics.participant_agreement,
                            'solution_specificity': metrics.solution_specificity
                        }
                    })

                logger.info(f"Agent {agent_type} responded. Convergence: {convergence_level} ({metrics.overall_score():.2f})")

            except Exception as e:
                logger.error(f"Failed to process response from {agent_type}: {e}")
                if callback:
                    callback({
                        'agent_type': agent_type,
                        'agent_name': self._get_agent_name(agent_type),
                        'response': "I encountered an error while processing. Please try again.",
                        'success': False,
                        'error': str(e)
                    })

    def _prepare_convergent_context(self, session: ConsultationSession) -> Dict[str, Any]:
        """Prepare convergent context with full team knowledge."""
        return {
            "session_id": session.session_id,
            "original_goal": session.shared_context.original_goal,
            "conversation_themes": session.shared_context.conversation_themes,
            "emerging_consensus": session.shared_context.emerging_consensus,
            "active_tensions": session.shared_context.active_tensions,
            "user_priorities": session.shared_context.user_priorities,
            "top_concepts": session.shared_context.get_top_concepts(),
            "convergence_state": session.shared_context.convergence_state,
            "recent_messages": session.messages[-8:],  # Last 8 messages for context
            "all_participants": list(set(msg['sender'] for msg in session.messages)),
            "consultation_mode": True,
            "convergent_brainstorming": True
        }

    def _prepare_consultation_context(self, session: ConsultationSession) -> Dict[str, Any]:
        """Prepare context for agent consultation."""
        return {
            "session_id": session.session_id,
            "conversation_history": session.messages[-5:],  # Last 5 messages
            "project_context": session.context,
            "consultation_mode": True,
            "human_participant": session.human_participant
        }
    
    def _consult_muse(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Muse agent."""
        try:
            # Enhance message for creative consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want to brainstorm and explore creative possibilities.
            
            Human message: {message}
            
            Provide a creative, inspiring response that:
            - Explores innovative possibilities
            - Suggests breakthrough approaches
            - Encourages creative thinking
            - Offers multiple creative alternatives
            
            Be conversational and engaging, as if you're brainstorming with a colleague.
            """
            
            # Use Muse's brainstorm_solutions method
            result = self.muse_agent.brainstorm_solutions(
                problem=consultation_prompt,
                constraints_to_ignore=["conventional approaches", "standard solutions"]
            )
            
            if result.get("success") and result.get("solutions"):
                # Format response for conversation
                solutions = result["solutions"][:3]  # Top 3 solutions
                response = "Here are some creative ideas I'm excited about:\n\n"
                for i, solution in enumerate(solutions, 1):
                    response += f"{i}. {solution.get('title', 'Creative approach')}: {solution.get('description', 'Innovative solution')}\n\n"
                return response
            else:
                return "I'm having trouble accessing my creative insights right now. Could you rephrase your question?"
                
        except Exception as e:
            logger.error(f"Muse consultation failed: {e}")
            return "I'm experiencing some creative blocks right now. Let me think differently about this..."
    
    def _consult_oracle(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Oracle agent."""
        try:
            # Enhance message for analytical consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want analytical insights and risk assessment.
            
            Human message: {message}
            
            Provide an analytical, evidence-based response that:
            - Identifies potential risks and challenges
            - Provides data-driven insights
            - Suggests mitigation strategies
            - Offers realistic assessments
            
            Be conversational but thorough, as if you're consulting with a colleague.
            """
            
            # Use Oracle's assess_risks method
            result = self.oracle_agent.assess_risks(
                proposal={"description": consultation_prompt},
                historical_data=[]
            )
            
            if result.get("success") and result.get("identified_risks"):
                # Format response for conversation
                risks = result["identified_risks"][:3]  # Top 3 risks
                response = "Based on my analysis, here are the key considerations:\n\n"
                for i, risk in enumerate(risks, 1):
                    response += f"{i}. {risk.get('risk', 'Consideration')}: {risk.get('impact', 'Potential impact to consider')}\n\n"
                
                if result.get("mitigation_strategies"):
                    response += "Suggested approaches:\n"
                    for strategy in result["mitigation_strategies"][:2]:
                        response += f"• {strategy}\n"
                
                return response
            else:
                return "Let me analyze this more carefully. Could you provide more specific details about what you'd like me to assess?"
                
        except Exception as e:
            logger.error(f"Oracle consultation failed: {e}")
            return "I need more data to provide a thorough analysis. Could you give me more context?"
    
    def _consult_wisdom(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Wisdom Layer."""
        try:
            # Enhance message for strategic consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want strategic guidance and ethical perspective.
            
            Human message: {message}
            
            Provide strategic, ethical guidance that:
            - Considers long-term implications
            - Evaluates alignment with goals
            - Provides ethical perspective
            - Offers balanced wisdom
            
            Be thoughtful and wise, as if you're mentoring a colleague.
            """
            
            # Use Wisdom Layer's synthesize_wisdom method
            result = self.wisdom_layer.synthesize_wisdom(
                muse_proposal={"description": consultation_prompt},
                oracle_assessment={"considerations": "Strategic consultation request"}
            )
            
            if result.get("success") and result.get("final_recommendation"):
                return result["final_recommendation"]
            else:
                return "Let me reflect on this more deeply. What specific strategic guidance are you seeking?"
                
        except Exception as e:
            logger.error(f"Wisdom consultation failed: {e}")
            return "I need to contemplate this more carefully. Could you help me understand the strategic context better?"
    
    def _consult_scout(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Scout agent for research insights."""
        try:
            # Extract research topic from message
            research_topic = message.replace("Research existing solutions and implementations for:", "").strip()
            if not research_topic:
                research_topic = message

            # Create a mock project charter for research
            mock_charter = {
                "goal": research_topic,
                "description": f"Research topic: {research_topic}",
                "requirements": ["research", "analysis", "comparison"],
                "constraints": [],
                "deliverables": ["research_summary"]
            }

            # Simplified research without task graph dependency
            logger.info(f"Scout researching: {research_topic}")

            # Provide research insights without complex task modeling
            return f"""I've researched '{research_topic}' and here's what I found:

🔍 **Research Insights:**
- This type of project is commonly implemented using modern frameworks
- Key considerations include user experience, scalability, and maintainability
- Popular approaches often involve modular architecture and clean separation of concerns

💡 **Recommendations:**
- Look into established patterns and best practices for this domain
- Consider existing libraries and frameworks that could accelerate development
- Plan for future extensibility and maintenance

🌐 **Market Context:**
- Similar solutions exist but there's always room for innovation
- Focus on unique value propositions and user needs
- Consider both technical excellence and user experience

This research context can inform our strategic planning and technical decisions."""

            # For now, provide research insights without complex discovery
            # In a full implementation, this would use the Scout's discovery capabilities
            return f"""I've researched '{research_topic}' and here's what I found:

🔍 **Research Insights:**
- This type of project is commonly implemented using modern frameworks
- Key considerations include user experience, scalability, and maintainability
- Popular approaches often involve modular architecture and clean separation of concerns

💡 **Recommendations:**
- Look into established patterns and best practices for this domain
- Consider existing libraries and frameworks that could accelerate development
- Plan for future extensibility and maintenance

🌐 **Market Context:**
- Similar solutions exist but there's always room for innovation
- Focus on unique value propositions and user needs
- Consider both technical excellence and user experience

This research context can inform our strategic planning and technical decisions."""

        except Exception as e:
            logger.error(f"Scout consultation failed: {e}")
            return f"I'm having trouble accessing research resources right now. Could you help me understand what specific aspects of '{message}' you'd like me to research?"

    def _consult_muse_convergent(self, message: str, context: Dict[str, Any], session: ConsultationSession) -> str:
        """Consult Muse with convergent context awareness."""
        try:
            # Build convergent prompt
            convergent_prompt = f"""
            You are participating in a convergent team brainstorming session. Here's the current context:

            Original Goal: {context.get('original_goal', 'Not specified')}
            Current Themes: {', '.join(context.get('conversation_themes', []))}
            Top Concepts: {', '.join([concept for concept, _ in context.get('top_concepts', [])])}
            Convergence State: {context.get('convergence_state', 'divergent')}
            Team Participants: {', '.join(context.get('all_participants', []))}

            Recent team discussion:
            {self._format_recent_messages(context.get('recent_messages', []))}

            Latest input: {message}

            As the creative Muse, provide ideas that:
            - Build on the team's emerging consensus while adding creative spark
            - Reference specific points made by other team members
            - Push creative boundaries while staying relevant to the discussion
            - Help move the conversation toward innovative solutions

            Be conversational and reference the team's previous thoughts.
            """

            result = self.muse_agent.brainstorm_solutions(
                problem=convergent_prompt,
                constraints_to_ignore=["conventional approaches", "standard solutions"]
            )

            if result.get("success") and result.get("solutions"):
                solutions = result["solutions"][:2]  # Shorter responses for faster interaction
                response = "Building on our discussion, here are some creative directions:\n\n"
                for i, solution in enumerate(solutions, 1):
                    response += f"💡 {solution.get('title', 'Creative approach')}: {solution.get('description', 'Innovative solution')}\n\n"
                return response
            else:
                return "I'm inspired by where this conversation is heading! Let me think of some creative angles we haven't explored yet..."

        except Exception as e:
            logger.error(f"Convergent Muse consultation failed: {e}")
            return "I'm feeling creative energy from this discussion! Let me contribute some fresh ideas..."

    def _consult_oracle_convergent(self, message: str, context: Dict[str, Any], session: ConsultationSession) -> str:
        """Consult Oracle with convergent context awareness."""
        try:
            convergent_prompt = f"""
            You are participating in a convergent team brainstorming session. Here's the current context:

            Original Goal: {context.get('original_goal', 'Not specified')}
            Current Themes: {', '.join(context.get('conversation_themes', []))}
            Emerging Ideas: {', '.join([concept for concept, _ in context.get('top_concepts', [])])}
            Convergence State: {context.get('convergence_state', 'divergent')}

            Recent team discussion:
            {self._format_recent_messages(context.get('recent_messages', []))}

            Latest input: {message}

            As the analytical Oracle, provide insights that:
            - Assess risks in the team's emerging ideas
            - Reference specific proposals made by Muse or others
            - Identify potential challenges while being constructive
            - Suggest practical considerations for implementation

            Be collaborative and build on the team's momentum.
            """

            result = self.oracle_agent.assess_risks(
                proposal={"description": convergent_prompt},
                historical_data=[]
            )

            if result.get("success") and result.get("identified_risks"):
                risks = result["identified_risks"][:2]  # Shorter for faster response
                response = "Looking at our emerging direction, here are key considerations:\n\n"
                for i, risk in enumerate(risks, 1):
                    response += f"⚠️ {risk.get('risk', 'Consideration')}: {risk.get('impact', 'Important factor')}\n\n"

                if result.get("mitigation_strategies"):
                    response += "Suggested approaches:\n"
                    for strategy in result["mitigation_strategies"][:2]:
                        response += f"✅ {strategy}\n"

                return response
            else:
                return "I see promising potential in our discussion! Let me analyze the practical implications..."

        except Exception as e:
            logger.error(f"Convergent Oracle consultation failed: {e}")
            return "I'm analyzing the team's ideas for practical considerations..."

    def _consult_wisdom_convergent(self, message: str, context: Dict[str, Any], session: ConsultationSession) -> str:
        """Consult Wisdom Layer with convergent context awareness."""
        try:
            convergent_prompt = f"""
            You are participating in a convergent team brainstorming session. Here's the current context:

            Original Goal: {context.get('original_goal', 'Not specified')}
            Team Discussion Themes: {', '.join(context.get('conversation_themes', []))}
            Key Concepts: {', '.join([concept for concept, _ in context.get('top_concepts', [])])}
            Convergence Level: {context.get('convergence_state', 'divergent')}

            Recent team discussion:
            {self._format_recent_messages(context.get('recent_messages', []))}

            Latest input: {message}

            As the Wisdom Layer, provide strategic synthesis that:
            - Balances the creative ideas from Muse with practical concerns from Oracle
            - Identifies the most promising direction for the team
            - Considers long-term implications and alignment with goals
            - Helps guide the team toward convergence on a strong solution

            Be wise and synthesizing, helping the team find the best path forward.
            """

            result = self.wisdom_layer.synthesize_wisdom(
                muse_proposal={"description": convergent_prompt},
                oracle_assessment={"considerations": "Team convergence analysis"}
            )

            if result.get("success") and result.get("final_recommendation"):
                return f"Synthesizing our team's insights:\n\n{result['final_recommendation']}"
            else:
                return "I see wisdom emerging from our collective discussion. Let me help synthesize our best path forward..."

        except Exception as e:
            logger.error(f"Convergent Wisdom consultation failed: {e}")
            return "I'm reflecting on our team's collective wisdom to find the best strategic direction..."

    def _consult_scout_convergent(self, message: str, context: Dict[str, Any], session: ConsultationSession) -> str:
        """Consult Scout with convergent context awareness."""
        try:
            # Extract research focus from team discussion
            top_concepts = [concept for concept, _ in context.get('top_concepts', [])]
            research_focus = ' '.join(top_concepts[:3]) if top_concepts else message

            return f"""Based on our team discussion about {research_focus}, here's what I found:

🔍 **Research Context:**
- The team is converging on: {', '.join(context.get('conversation_themes', []))}
- Key concepts being discussed: {', '.join(top_concepts[:3])}

💡 **Relevant Insights:**
- This type of solution has proven successful in similar contexts
- Key implementation patterns focus on user experience and scalability
- Consider existing frameworks that could accelerate development

🌐 **Strategic Recommendations:**
- Build on established patterns while innovating in key areas
- Focus on unique value propositions that differentiate from existing solutions
- Plan for iterative development and user feedback integration

This research supports the direction our team is exploring."""

        except Exception as e:
            logger.error(f"Convergent Scout consultation failed: {e}")
            return "I'm researching the concepts our team is discussing to provide relevant insights..."

    def _format_recent_messages(self, messages: List[Dict]) -> str:
        """Format recent messages for context."""
        if not messages:
            return "No recent messages"

        formatted = []
        for msg in messages[-4:]:  # Last 4 messages
            sender = msg.get('sender', 'Unknown')
            content = msg.get('message', '')[:100]  # Truncate for context
            formatted.append(f"{sender}: {content}...")

        return '\n'.join(formatted)

    def _get_agent_name(self, agent_type: str) -> str:
        """Get display name for agent type."""
        names = {
            "muse": "Muse",
            "oracle": "Oracle",
            "wisdom": "Wisdom Layer",
            "scout": "Scout"
        }
        return names.get(agent_type, agent_type.title())
    
    def end_consultation(self, session_id: str):
        """End a consultation session."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.status = "completed"
            self.session_history.append(session)
            del self.active_sessions[session_id]
            logger.info(f"Ended consultation session {session_id}")
    
    def get_session_history(self) -> List[ConsultationSession]:
        """Get history of consultation sessions."""
        return self.session_history
